graph TD
    subgraph Entidades
        M[Mecánico]
        V[Vehículo]
        P[Procedimiento]
    end

    subgraph Atributos
        M --> M_nombre[nombre]
        M --> M_contacto[contacto]
        M --> M_especialidad[especialidad]

        V --> V_patente[patente (única)]
        V --> V_modelo[modelo]
        V --> V_año[año]
        V --> V_dueño[dueño]

        P --> P_nombre[nombre]
        P --> P_desc[descripción]
    end

    subgraph Relaciones
        P -- "tiene" --> M
        P -- "se realiza sobre" --> V
    end

    style V_patente fill:#f9f,stroke:#333,stroke-width:2px,font-weight:bold
    style M_nombre fill:#f9f,stroke:#333,stroke-width:2px
    style M_contacto fill:#f9f,stroke:#333,stroke-width:2px
    style M_especialidad fill:#f9f,stroke:#333,stroke-width:2px
    style V_patente fill:#f9f,stroke:#333,stroke-width:2px
    style V_modelo fill:#f9f,stroke:#333,stroke-width:2px
    style V_año fill:#f9f,stroke:#333,stroke-width:2px
    style V_dueño fill:#f9f,stroke:#333,stroke-width:2px
    style P_nombre fill:#f9f,stroke:#333,stroke-width:2px
    style P_desc fill:#f9f,stroke:#333,stroke-width:2px
    style M stroke-width:3px,stroke:#666
    style V stroke-width:3px,stroke:#666
    style P stroke-width:3px,stroke:#666
