graph TD
    subgraph Criterios
        C1["1.1.1 Variables y operaciones"]
        C2["1.1.2 Estructuras y operadores"]
        C3["1.1.3 Paquetes externos"]
        C4["1.1.4 Aplicación en Django"]
        C5["Presentación y entrega"]
    end

    subgraph Logro
        L1[Logrado (7 pts)]
        L2[En Proceso (5 pts)]
        L3[No Logrado (0-1 pts)]
    end

    C1 --> L1
    C1 --> L2
    C1 --> L3

    C2 --> L1
    C2 --> L2
    C2 --> L3

    C3 --> L1
    C3 --> L2
    C3 --> L3

    C4 --> L1
    C4 --> L2
    C4 --> L3
    
    C5 --> L1
    C5 --> L2
    C5 --> L3

    style L1 fill:#0f0,stroke:#333,stroke-width:2px,font-weight:bold
    style L2 fill:#ff0,stroke:#333,stroke-width:2px,font-weight:bold
    style L3 fill:#f00,stroke:#333,stroke-width:2px,font-weight:bold
