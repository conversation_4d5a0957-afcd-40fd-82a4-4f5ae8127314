from django.contrib import admin
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Procedimiento

@admin.register(Mecanico)
class MecanicoAdmin(admin.ModelAdmin):
	list_display = ('nombre', 'contacto', 'especialidad')
	search_fields = ('nombre', 'especialidad')
	list_filter = ('especialidad',)

@admin.register(Vehiculo)
class VehiculoAdmin(admin.ModelAdmin):
	list_display = ('patente', 'modelo', 'año', 'dueño')
	search_fields = ('patente', 'modelo', 'dueño')
	list_filter = ('modelo', 'año')

@admin.register(Procedimiento)
class ProcedimientoAdmin(admin.ModelAdmin):
	list_display = ('nombre', 'mecanico', 'vehiculo')
	search_fields = ('nombre', 'descripcion', 'mecanico__nombre', 'vehiculo__patente')
	list_filter = ('mecanico', 'vehiculo')
