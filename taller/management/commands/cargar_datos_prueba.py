from django.core.management.base import BaseCommand
from taller.models import <PERSON><PERSON><PERSON>, <PERSON>eh<PERSON>ulo, Procedimiento

class Command(BaseCommand):
    help = 'Carga datos de prueba para Mecánico, Vehículo y Procedimiento'

    def handle(self, *args, **kwargs):
        # Crear mecánicos
        m1 = Mecanico.objects.create(nombre='<PERSON>', contacto='<EMAIL>', especialidad='Motor')
        m2 = Mecanico.objects.create(nombre='Ana Gómez', contacto='<EMAIL>', especialidad='Frenos')

        # Crear vehículos
        v1 = Vehiculo.objects.create(patente='ABC123', modelo='Toyota Corolla', año=2015, dueño='<PERSON>')
        v2 = Vehiculo.objects.create(patente='XYZ789', modelo='Ford Fiesta', año=2018, dueño='Luc<PERSON> Torres')

        # Crear procedimientos
        Procedimiento.objects.create(nombre='Cambio de aceite', descripcion='Cambio de aceite y filtro', mecanico=m1, vehiculo=v1)
        Procedimiento.objects.create(nombre='Revisión de frenos', descripcion='Inspección y ajuste de frenos', mecanico=m2, vehiculo=v2)

        self.stdout.write(self.style.SUCCESS('Datos de prueba cargados correctamente.'))
