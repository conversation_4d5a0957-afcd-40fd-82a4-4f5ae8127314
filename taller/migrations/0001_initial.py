# Generated by Django 5.2.5 on 2025-08-27 00:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Mecanico',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('contacto', models.CharField(max_length=100)),
                ('especialidad', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Vehiculo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('patente', models.CharField(max_length=20, unique=True)),
                ('modelo', models.Char<PERSON>ield(max_length=100)),
                ('año', models.PositiveIntegerField()),
                ('dueño', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Procedimiento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('descripcion', models.TextField()),
                ('mecanico', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='taller.mecanico')),
                ('vehiculo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='taller.vehiculo')),
            ],
        ),
    ]
