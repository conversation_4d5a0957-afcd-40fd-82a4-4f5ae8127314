from django.db import models

class Mecanico(models.Model):
	nombre = models.Char<PERSON>ield(max_length=100)
	contacto = models.CharField(max_length=100)
	especialidad = models.CharField(max_length=100)

	def __str__(self):
		return self.nombre

class Vehiculo(models.Model):
	patente = models.CharField(max_length=20, unique=True)
	modelo = models.CharField(max_length=100)
	año = models.PositiveIntegerField()
	dueño = models.CharField(max_length=100)

	def __str__(self):
		return f"{self.patente} - {self.modelo}"

class Procedimiento(models.Model):
	nombre = models.Char<PERSON>ield(max_length=100)
	descripcion = models.TextField()
	mecanico = models.ForeignKey(Mecanico, on_delete=models.CASCADE)
	vehiculo = models.ForeignKey(Vehiculo, on_delete=models.CASCADE)

	def __str__(self):
		return self.nombre
