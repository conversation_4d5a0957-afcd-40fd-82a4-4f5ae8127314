from django.shortcuts import render


from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ced<PERSON><PERSON><PERSON>

def mecanicos_list(request):
	mecanicos = Mecanico.objects.all()
	return render(request, 'taller/mecanicos_list.html', {'mecanicos': mecanicos})

def vehiculos_list(request):
	vehiculos = Vehiculo.objects.all()
	return render(request, 'taller/vehiculos_list.html', {'vehiculos': vehiculos})

def procedimientos_list(request):
	procedimientos = Procedimiento.objects.all()
	return render(request, 'taller/procedimientos_list.html', {'procedimientos': procedimientos})
